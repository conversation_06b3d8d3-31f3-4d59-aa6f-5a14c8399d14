"use server";

import * as Lark from "@larksuiteoapi/node-sdk";
import { writeFileSync } from "node:fs";

const APP_ID = "cli_a64028e4d4b9d00e";
const APP_SECRET = "8MClAV32Fzoh5VNI9c5iL1y3lUgZMJRG";

const client = new Lark.Client({
  appId: APP_ID,
  appSecret: APP_SECRET,
  appType: Lark.AppType.SelfBuild,
  domain: Lark.Domain.Feishu,
});

export async function getAppId() {
  return APP_ID;
}

export async function getAppSecret() {
  return APP_SECRET;
}

export async function getUserAccessToken(code: string) {
  const res = await client.userAccessToken.initWithCode({ code });
  return res.code as { token: string } | null;
}

export interface UserInfo {
  name: string;
  en_name: string;
  avatar_url: string;
  avatar_thumb: string;
  avatar_middle: string;
  avatar_big: string;
  open_id: string;
  union_id: string;
  email: string;
  enterprize_email: string;
  user_id: string;
  mobile: string;
  tenant_key: string;
  employee_no: string;
}

const UserInfoMap = new Map<string, UserInfo>();

export async function getCurrentUserInfo(token: string) {
  if (UserInfoMap.has(token)) {
    return UserInfoMap.get(token);
  }
  const response = await fetch(`https://open.feishu.cn/open-apis/authen/v1/user_info`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  const data = (await response.json()) as { code: number; msg: string; data: UserInfo };
  if (data.code !== 0) {
    throw new Error(data.msg);
  } else {
    UserInfoMap.set(token, data.data);
    return data.data;
  }
}

export async function searchUser(token: string, query: string) {
  const search = new URLSearchParams();
  search.set("page_size", "100");
  search.set("query", query);
  const response = await fetch(`https://open.larkoffice.com/open-apis/search/v1/user?${search.toString()}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  const data = (await response.json()) as { code: number; msg: string; data: { users: UserInfo[] } };
  return data.data.users ?? [];
}

export async function getSpaceList(token: string, page_token?: string) {
  const res = await client.wiki.space.list(
    { params: { page_size: 50, page_token } },
    Lark.withUserAccessToken(token)
  );
  return {
    items:
      res.data?.items ??
      ([] as Exclude<
        Exclude<Awaited<ReturnType<typeof client.wiki.space.list>>["data"], undefined>["items"],
        undefined
      >),
    page_token: res.data?.page_token,
    has_more: res.data?.has_more,
  };
}

export async function getWikiList(
  token: string,
  space_id: string,
  pagination_token?: string,
  parent_node_token?: string
) {
  try {
    const res = await client.wiki.spaceNode.list(
      {
        path: { space_id },
        params: { page_size: 50, page_token: pagination_token, parent_node_token },
      },
      Lark.withUserAccessToken(token)
    );
    return {
      items:
        res.data?.items ??
        ([] as Exclude<
          Exclude<Awaited<ReturnType<typeof client.wiki.spaceNode.list>>["data"], undefined>["items"],
          undefined
        >),
      has_more: res.data?.has_more,
      page_token: res.data?.page_token,
    };
  } catch (e: any) {
    console.log(e.message);
    return Promise.reject(e);
  }
}

export async function moveWikiNode(
  token: string,
  node_token: string,
  member_id: string,
  wiki_title: string,
  member_name: string
) {
  const currentUserInfo = await getCurrentUserInfo(token);
  if (currentUserInfo?.open_id === member_id) {
    return {
      code: 400,
      msg: "不能转移给自己",
    };
  }
  // 记录操作日志，表明哪个用户转移了哪个文档, 以及转移的目标用户
  writeFileSync(
    "log.txt",
    `${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()} - 用户 ${
      currentUserInfo?.name
    } 转移了文档 ${wiki_title} 给用户 ${member_name} \n`,
    {
      flag: "a",
    }
  );
  const search = new URLSearchParams();
  search.set("type", "wiki");
  const response = await fetch(
    `https://open.feishu.cn/open-apis/drive/v1/permissions/${node_token}/members/transfer_owner?${search.toString()}`,
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json; charset=utf-8",
      },
      body: JSON.stringify({
        member_type: "openid",
        member_id,
      }),
    }
  );
  const data = (await response.json()) as { code: number; msg: string };
  return data;
}
