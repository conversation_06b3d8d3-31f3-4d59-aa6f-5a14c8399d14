import { Select } from "antd";
import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { getSpaceList } from "../api/sdk";
import { useAccessToken } from "../hooks/storage";

type Space = Awaited<ReturnType<typeof getSpaceList>>["items"][0];

export const SpaceSelect: FC<{ value?: string; onChange: (val: string) => void }> = ({ value, onChange }) => {
  const [spaceList, setSpaceList] = useState<Space[]>();
  const [accessToken] = useAccessToken();
  const [nextPageToken, setNextPageToken] = useState<string>();
  const [loading, setLoading] = useState(false);
  const [end, setEnd] = useState(false);

  const getNextPage = useCallback(async () => {
    if (!accessToken?.token) return;
    setLoading(true);
    try {
      const res = await getSpaceList(accessToken.token, nextPageToken);
      if (!res.has_more) {
        setEnd(true);
      }
      if (res.page_token) {
        setNextPageToken(res.page_token);
      }
      setSpaceList(res.items ?? []);
    } finally {
      setLoading(false);
    }
  }, [accessToken?.token, nextPageToken]);

  const optionList = useMemo(
    () =>
      spaceList?.map((space) => ({
        label: space.name,
        value: space.space_id,
      })),
    [spaceList]
  );

  useEffect(() => {
    getNextPage();
  }, []);

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder="选择空间"
      loading={loading}
      options={optionList}
      popupMatchSelectWidth={false}
      onPopupScroll={(e) => {
        // 如果到底（距底部有100px也算到底）, 并且没有结束, 并且没有在加载中, 就加载下一页
        if (
          e.currentTarget.scrollHeight - e.currentTarget.scrollTop - e.currentTarget.clientHeight < 100 &&
          !end &&
          !loading
        ) {
          setLoading(true);
          getNextPage();
        }
      }}
    />
  );
};
