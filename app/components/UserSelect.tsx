import { Select } from "antd";
import { FC, useState } from "react";
import { useDebounce } from "react-use";
import { searchUser } from "../api/sdk";
import { useAccessToken } from "../hooks/storage";

interface UserSelectProps {
  value?: string;
  onChange: (value: string) => void;
  userName?: string;
  onUserNameChange?: (value: string) => void;
}

export const UserSelect: FC<UserSelectProps> = ({ value, onChange, onUserNameChange }) => {
  const [accessToken] = useAccessToken();
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<{ label: string; value: string }[]>([]);

  const getList = async (token: string, searchKey: string) => {
    const reg = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/;
    if (!reg.test(searchKey)) return [];
    if (!searchKey) return [];
    const list = await searchUser(token!, searchKey);
    return list.map((item) => ({ label: item.name, value: item.open_id }));
  };

  useDebounce(
    async () => {
      setLoading(true);
      try {
        const list = await getList(accessToken?.token!, search);
        console.log(list);
        setOptions(list);
      } finally {
        setLoading(false);
      }
    },
    500,
    [search]
  );

  return (
    <Select
      value={value}
      onChange={(val) => {
        onChange(val);
        onUserNameChange && onUserNameChange(options.find((item) => item.value === val)?.label || "");
      }}
      showSearch
      filterOption={false}
      placeholder="搜索用户"
      loading={loading}
      onSearch={setSearch}
      options={options}
      popupMatchSelectWidth={false}
    />
  );
};
