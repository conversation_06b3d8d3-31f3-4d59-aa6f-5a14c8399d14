import lscache from "lscache";
import { useCallback, useEffect, useState } from "react";
import { useAsync, useSearchParam } from "react-use";
import { getAppId, getUserAccessToken } from "../api/sdk";

type AccessToken = { token: string } | null;

export function useAccessToken(): [AccessToken, (value: AccessToken) => void] {
  const urlCode = useSearchParam("code");
  const [value, setValue] = useState(lscache.get("access_token") || null);

  useEffect(() => {
    const data = lscache.get("access_token") || null;
    if (data) {
      setValue(data);
    }
  }, []);

  const set = useCallback((value: AccessToken) => {
    lscache.set("access_token", value, 110);
    setValue(value);
  }, []);

  useAsync(async () => {
    if (!value && !urlCode) {
      const appId = await getAppId();
      window.location.href = `https://open.feishu.cn/open-apis/authen/v1/authorize?app_id=${appId}&redirect_uri=${
        window.location.origin
      }&scope=${["wiki:wiki", "contact:user:search", "contact:contact.base:readonly"].join("%20")}`;
    }
  }, [value, urlCode]);

  useAsync(async () => {
    if (urlCode && !value) {
      const res = (await getUserAccessToken(urlCode)) as AccessToken;
      if (res) {
        set(res);
        location.href = "/";
      }
    }
  }, [urlCode, set, value]);

  return [value, set];
}
