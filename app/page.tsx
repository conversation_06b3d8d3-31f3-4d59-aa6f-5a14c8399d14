"use client";
import { <PERSON><PERSON>, message, Tree } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useAsync } from "react-use";
import {
  concatMap,
  delay,
  EMPTY,
  expand,
  firstValueFrom,
  from,
  map,
  Observable,
  of,
  tap,
  toArray,
} from "rxjs";
import type { Cache } from "swr";
import { SWRConfig } from "swr";
import { getCurrentUserInfo, getWikiList, moveWikiNode } from "./api/sdk";
import { SpaceSelect } from "./components/SpaceSelect";
import { UserSelect } from "./components/UserSelect";
import { useAccessToken } from "./hooks/storage";

type WikiItem = Awaited<ReturnType<typeof getWikiList>>["items"][0] & { key: string; children?: WikiItem[] };

function localStorageProvider(): Cache {
  // When initializing, we restore the data from `localStorage` into a map.
  const map: Map<string, any> = new Map(JSON.parse(globalThis.localStorage.getItem("app-cache") || "[]"));

  // Before unloading the app, we write back all the data into `localStorage`.
  window.addEventListener("beforeunload", () => {
    const appCache = JSON.stringify(Array.from(map.entries()));
    globalThis.localStorage.setItem("app-cache", appCache);
  });

  // We still use the map for write & read for performance.
  return map;
}

export default function Home() {
  const [accessToken, setAccessToken] = useAccessToken();
  const [spaceId, setSpaceId] = useState<string>();
  const [wikiList, setWikiList] = useState<WikiItem[]>();
  const [userInfo, setUserInfo] = useState<any>();
  const [selectedUser, setSelectedUser] = useState<string>();
  const [selectedUserName, setSelectedUserName] = useState<string>();
  const [actionLoading, setActionLoading] = useState(false);
  const [selectedWikiListOne, setSelectedWikiListOne] = useState<WikiItem[]>([]);
  const [selectedWikiListTwo, setSelectedWikiListTwo] = useState<WikiItem[]>([]);
  const [selectedWikiListThree, setSelectedWikiListThree] = useState<WikiItem[]>([]);

  const sliceLength = useMemo(() => Math.round((wikiList?.length ?? 0) / 3), [wikiList]);

  const selectedWikiList = useMemo(
    () => [...selectedWikiListOne, ...selectedWikiListTwo, ...selectedWikiListThree],
    [selectedWikiListOne, selectedWikiListTwo, selectedWikiListThree]
  );

  useAsync(async () => {
    const info = await getCurrentUserInfo(accessToken?.token!);
    setUserInfo(info);
  }, [accessToken?.token]);

  const getList = useCallback(
    async (space_id: string) => {
      if (accessToken?.token) {
        const list = await getAllList(userInfo.open_id, accessToken.token, space_id);
        setWikiList(list);
      }
    },
    [accessToken?.token, userInfo?.open_id]
  );

  useEffect(() => {
    if (spaceId) {
      getList(spaceId);
    }
  }, [spaceId, getList]);

  return (
    <SWRConfig value={accessToken?.token ? { provider: localStorageProvider } : {}}>
      <div className="flex flex-col gap-4 p-4">
        <div className="flex items-center gap-2">
          <span>选择空间</span>
          <SpaceSelect value={spaceId} onChange={setSpaceId} />
          <span className="mx-2"></span>
          <span>选择新的所有者</span>
          <UserSelect
            value={selectedUser}
            onChange={setSelectedUser}
            onUserNameChange={setSelectedUserName}
          />
          <span className="mx-3"></span>
          <Button
            type="primary"
            disabled={!selectedUser || !selectedWikiList.length}
            loading={actionLoading}
            onClick={async () => {
              if (actionLoading) return;
              setActionLoading(true);
              try {
                await firstValueFrom(
                  of(...selectedWikiList).pipe(
                    concatMap(async (item) => {
                      const data = await moveWikiNode(
                        accessToken!.token,
                        item.node_token!,
                        selectedUser!,
                        item.title!,
                        selectedUserName!
                      );
                      if (data.code !== 0) {
                        message.error(data.msg);
                        throw new Error(data.msg);
                      }
                      return item.node_token;
                    }),
                    delay(60000 / 100),
                    tap((item) => {
                      setSelectedWikiListOne((prev) => prev.filter((node) => node.node_token !== item));
                      setSelectedWikiListTwo((prev) => prev.filter((node) => node.node_token !== item));
                      setSelectedWikiListThree((prev) => prev.filter((node) => node.node_token !== item));
                      setWikiList((prev) =>
                        prev?.map((node) =>
                          modifyTree(
                            node,
                            (node) => node.node_token === item,
                            () => ({ ...selectedWikiList[0], owner: selectedUser, disabled: true })
                          )
                        )
                      );
                      message.success(
                        `【${selectedWikiList.find((i) => i.node_token === item)?.title}】转移成功`
                      );
                    }),
                    toArray()
                  )
                );
              } finally {
                setActionLoading(false);
              }
            }}
          >
            转移
          </Button>
          <Button
            danger
            onClick={() => {
              setAccessToken(null);
              location.href = "/";
            }}
          >
            重新授权
          </Button>
        </div>
        {wikiList?.length ? (
          <div className="flex gap-10">
            <Tree<WikiItem>
              className="w-1/3"
              treeData={wikiList.slice(0, sliceLength)}
              checkable
              checkStrictly={true}
              checkedKeys={selectedWikiList.map((item) => item.key)}
              onCheck={(...[, { checkedNodes }]) => {
                setSelectedWikiListOne(checkedNodes);
              }}
              loadData={async (node) => {
                const list = await getAllList(
                  userInfo.open_id,
                  accessToken!.token,
                  spaceId!,
                  node.node_token
                );
                // setSelectedWikiListOne((prev) => [
                //   ...prev,
                //   ...list.filter((item) => item.owner === userInfo.open_id),
                // ]);
                setWikiList((prev) =>
                  prev?.map((item) =>
                    modifyTree(
                      item,
                      (item) => item.node_token === node.node_token,
                      () => ({ ...node, children: list })
                    )
                  )
                );
              }}
              titleRender={(node) => {
                return (
                  <div className="inline-flex items-center">
                    <span>{node.title}</span>
                    {/* @ts-ignore */}
                    {node.children?.length ? (
                      <Button
                        className="p-0 ml-3"
                        type="link"
                        onClick={(e) => {
                          e.stopPropagation();
                          const list = flatTree(node);
                          setSelectedWikiListOne((prev) => [
                            ...prev,
                            ...(list as WikiItem[]).filter(
                              (item) =>
                                item.owner === userInfo.open_id &&
                                !prev.some((node) => node.node_token === item.node_token)
                            ),
                          ]);
                        }}
                      >
                        全选
                      </Button>
                    ) : null}
                  </div>
                );
              }}
            />
            <Tree<WikiItem>
              className="w-1/3"
              treeData={wikiList.slice(sliceLength, sliceLength * 2)}
              checkable
              checkStrictly={true}
              checkedKeys={selectedWikiList.map((item) => item.key)}
              onCheck={(...[, { checkedNodes }]) => {
                setSelectedWikiListTwo(checkedNodes);
              }}
              loadData={async (node) => {
                const list = await getAllList(
                  userInfo.open_id,
                  accessToken!.token,
                  spaceId!,
                  node.node_token
                );
                // setSelectedWikiListTwo((prev) => [
                //   ...prev,
                //   ...list.filter((item) => item.owner === userInfo.open_id),
                // ]);
                setWikiList((prev) =>
                  prev?.map((item) =>
                    modifyTree(
                      item,
                      (item) => item.node_token === node.node_token,
                      () => ({ ...node, children: list })
                    )
                  )
                );
              }}
              titleRender={(node) => {
                return (
                  <div className="inline-flex items-center">
                    <span>{node.title}</span>
                    {/* @ts-ignore */}
                    {node.children?.length ? (
                      <Button
                        className="p-0 ml-3"
                        type="link"
                        onClick={(e) => {
                          e.stopPropagation();
                          const list = flatTree(node);
                          setSelectedWikiListTwo((prev) => [
                            ...prev,
                            ...(list as WikiItem[]).filter(
                              (item) =>
                                item.owner === userInfo.open_id &&
                                !prev.some((node) => node.node_token === item.node_token)
                            ),
                          ]);
                        }}
                      >
                        全选
                      </Button>
                    ) : null}
                  </div>
                );
              }}
            />
            <Tree<WikiItem>
              className="w-1/3"
              treeData={wikiList.slice(sliceLength * 2)}
              selectable={false}
              checkable
              checkStrictly={true}
              checkedKeys={selectedWikiList.map((item) => item.key)}
              onCheck={(...[, { checkedNodes }]) => {
                console.log("emit", checkedNodes);
                setSelectedWikiListThree(checkedNodes);
              }}
              loadData={async (node) => {
                const list = await getAllList(
                  userInfo.open_id,
                  accessToken!.token,
                  spaceId!,
                  node.node_token
                );
                // setSelectedWikiListThree((prev) => [
                //   ...prev,
                //   ...list.filter((item) => item.owner === userInfo.open_id),
                // ]);
                setWikiList((prev) =>
                  prev?.map((item) =>
                    modifyTree(
                      item,
                      (item) => item.node_token === node.node_token,
                      () => ({ ...node, children: list })
                    )
                  )
                );
              }}
              titleRender={(node) => {
                return (
                  <div className="inline-flex items-center">
                    <span>{node.title}</span>
                    {/* @ts-ignore */}
                    {node.children?.length ? (
                      <Button
                        className="p-0 ml-3"
                        type="link"
                        onClick={(e) => {
                          e.stopPropagation();
                          const list = flatTree(node);
                          setSelectedWikiListThree((prev) => [
                            ...prev,
                            ...(list as WikiItem[]).filter(
                              (item) =>
                                item.owner === userInfo.open_id &&
                                !prev.some((node) => node.node_token === item.node_token)
                            ),
                          ]);
                        }}
                      >
                        全选
                      </Button>
                    ) : null}
                  </div>
                );
              }}
            />
          </div>
        ) : null}
      </div>
    </SWRConfig>
  );
}

function modifyTree<T extends { children?: any[] }>(
  node: T,
  condition: (node: T) => boolean,
  modification: (node: T) => T
) {
  // 创建新的节点对象
  const newNode = { ...node };

  // 如果当前节点满足条件，则修改它
  if (condition(node)) {
    Object.assign(newNode, modification(node));
  }

  // 如果有子节点，递归处理
  if (Array.isArray(node.children)) {
    newNode.children = node.children.map((child) => modifyTree(child, condition, modification));
  }

  return newNode;
}

function getAllList(
  userId: string,
  token: string,
  spaceId: string,
  parent_token?: string
): Promise<WikiItem[]> {
  return firstValueFrom(
    from(getWikiList(token, spaceId, "", parent_token)).pipe(
      expand((res) => {
        if (res.has_more) {
          return from(getWikiList(token, spaceId, res.page_token, parent_token));
        }
        return EMPTY;
      }),
      concatMap((res) => res.items),
      toArray(),
      map((list) =>
        list
          .map((item) => ({
            ...item,
            title: item.title || "未命名文档",
            disabled: item.owner !== userId,
            key: item.node_token,
            isLeaf: !item.has_child,
          }))
          .filter((item) => (item.has_child ? true : item.owner === userId))
      )
    ) as Observable<WikiItem[]>
  );
}

function flatTree(node: WikiItem) {
  const stack = [node];
  const list = [];
  while (stack.length) {
    const item = stack.pop();
    if (item?.children?.length) {
      stack.push(...item.children);
    }
    if (item) {
      list.push(item);
    }
  }
  return list;
}
