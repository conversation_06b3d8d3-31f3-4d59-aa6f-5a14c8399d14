import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';

export enum OperationType {
  TRANSFER_START = 'transfer_start',
  TRANSFER_SUCCESS = 'transfer_success',
  TRANSFER_FAILED = 'transfer_failed'
}

@Entity('operation_logs')
export class OperationLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  task_id: number;

  @Column()
  user_name: string;

  @Column()
  wiki_title: string;

  @Column()
  target_user_name: string;

  @Column({
    type: 'varchar',
    enum: OperationType
  })
  operation_type: OperationType;

  @CreateDateColumn()
  created_at: Date;
}
