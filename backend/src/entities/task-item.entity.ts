import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Task } from './task.entity';

export enum TaskItemStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

@Entity('task_items')
export class TaskItem {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  task_id: number;

  @Column()
  node_token: string;

  @Column()
  wiki_title: string;

  @Column({
    type: 'varchar',
    enum: TaskItemStatus,
    default: TaskItemStatus.PENDING
  })
  status: TaskItemStatus;

  @Column({ nullable: true, type: 'text' })
  error_message: string;

  @Column({ nullable: true })
  processed_at: Date;

  @CreateDateColumn()
  created_at: Date;

  @ManyToOne(() => Task, task => task.items)
  @JoinColumn({ name: 'task_id' })
  task: Task;
}
