import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { TaskItem } from './task-item.entity';

export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

@Entity('tasks')
export class Task {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  user_id: string;

  @Column()
  space_id: string;

  @Column()
  target_user_id: string;

  @Column()
  target_user_name: string;

  @Column({
    type: 'varchar',
    enum: TaskStatus,
    default: TaskStatus.PENDING
  })
  status: TaskStatus;

  @Column()
  total_items: number;

  @Column({ default: 0 })
  completed_items: number;

  @Column({ default: 0 })
  failed_items: number;

  @CreateDateColumn()
  created_at: Date;

  @Column({ nullable: true })
  started_at: Date;

  @Column({ nullable: true })
  completed_at: Date;

  @Column({ nullable: true, type: 'text' })
  error_message: string;

  @OneToMany(() => TaskItem, taskItem => taskItem.task)
  items: TaskItem[];

  @UpdateDateColumn()
  updated_at: Date;
}
