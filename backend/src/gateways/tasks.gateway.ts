import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';

interface TaskProgressUpdate {
  task_id: number;
  status: string;
  completed_items: number;
  failed_items: number;
  current_item?: {
    node_token: string;
    title: string;
    status: string;
  };
}

@WebSocketGateway({
  cors: {
    origin: ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true,
  },
})
export class TasksGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(TasksGateway.name);
  private userSockets = new Map<string, Set<string>>(); // userId -> Set<socketId>

  handleConnection(client: Socket) {
    this.logger.log(`客户端连接: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`客户端断开连接: ${client.id}`);
    
    // 从用户socket映射中移除
    for (const [userId, sockets] of this.userSockets.entries()) {
      if (sockets.has(client.id)) {
        sockets.delete(client.id);
        if (sockets.size === 0) {
          this.userSockets.delete(userId);
        }
        break;
      }
    }
  }

  @SubscribeMessage('join-user-room')
  handleJoinUserRoom(client: Socket, userId: string) {
    this.logger.log(`用户 ${userId} 加入房间，socket: ${client.id}`);
    
    if (!this.userSockets.has(userId)) {
      this.userSockets.set(userId, new Set());
    }
    this.userSockets.get(userId).add(client.id);
    
    client.join(`user-${userId}`);
    client.emit('joined-room', { userId });
  }

  sendTaskUpdate(userId: string, update: TaskProgressUpdate) {
    this.logger.log(`发送任务更新给用户 ${userId}:`, update);
    this.server.to(`user-${userId}`).emit('task-update', update);
  }

  sendTaskComplete(userId: string, taskId: number) {
    this.logger.log(`任务 ${taskId} 完成，通知用户 ${userId}`);
    this.server.to(`user-${userId}`).emit('task-complete', { taskId });
  }

  sendTaskError(userId: string, taskId: number, error: string) {
    this.logger.log(`任务 ${taskId} 错误，通知用户 ${userId}: ${error}`);
    this.server.to(`user-${userId}`).emit('task-error', { taskId, error });
  }
}
