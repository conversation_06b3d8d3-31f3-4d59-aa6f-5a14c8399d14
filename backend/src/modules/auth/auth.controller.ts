import { Controller, Get, Query } from '@nestjs/common';
import { AuthService } from './auth.service';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Get('app-id')
  getAppId() {
    return { app_id: this.authService.getAppId() };
  }

  @Get('token')
  async getUserAccessToken(@Query('code') code: string) {
    const token = await this.authService.getUserAccessToken(code);
    return token;
  }
}
