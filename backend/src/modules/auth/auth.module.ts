import { Modu<PERSON> } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { AuthGuard } from './auth.guard';
import { TokenStorageService } from './token-storage.service';

@Module({
  controllers: [AuthController],
  providers: [AuthService, AuthGuard, TokenStorageService],
  exports: [AuthService, AuthGuard, TokenStorageService],
})
export class AuthModule {}
