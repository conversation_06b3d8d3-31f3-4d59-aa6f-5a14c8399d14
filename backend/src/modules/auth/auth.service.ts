import { Injectable } from '@nestjs/common';
import * as Lark from '@larksuiteoapi/node-sdk';
import { TokenStorageService } from './token-storage.service';

const APP_ID = "cli_a64028e4d4b9d00e";
const APP_SECRET = "8MClAV32Fzoh5VNI9c5iL1y3lUgZMJRG";

@Injectable()
export class AuthService {
  private client: Lark.Client;

  constructor(private tokenStorage: TokenStorageService) {
    this.client = new Lark.Client({
      appId: APP_ID,
      appSecret: APP_SECRET,
      appType: Lark.AppType.SelfBuild,
      domain: Lark.Domain.Feishu,
    });
  }

  getAppId() {
    return APP_ID;
  }

  async getUserAccessToken(code: string) {
    try {
      const res = await this.client.userAccessToken.initWithCode({ code });
      return res.code as { token: string } | null;
    } catch (error) {
      throw new Error('获取访问令牌失败');
    }
  }

  async validateToken(token: string) {
    try {
      const userInfo = await this.getCurrentUserInfo(token);
      // 存储用户token
      this.tokenStorage.setUserToken(userInfo.open_id, token);
      return { ...userInfo, token };
    } catch (error) {
      return null;
    }
  }

  private async getCurrentUserInfo(token: string) {
    try {
      const response = await fetch(`https://open.feishu.cn/open-apis/authen/v1/user_info`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const data = await response.json() as { code: number; msg: string; data: any };

      if (data.code !== 0) {
        throw new Error(data.msg);
      }

      return data.data;
    } catch (error) {
      throw error;
    }
  }
}
