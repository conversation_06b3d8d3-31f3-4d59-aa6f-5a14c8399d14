import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class TokenStorageService {
  private readonly logger = new Logger(TokenStorageService.name);
  private tokenMap = new Map<string, string>(); // userId -> accessToken

  setUserToken(userId: string, accessToken: string): void {
    this.tokenMap.set(userId, accessToken);
    this.logger.log(`存储用户 ${userId} 的访问令牌`);
  }

  getUserToken(userId: string): string | null {
    const token = this.tokenMap.get(userId);
    if (!token) {
      this.logger.warn(`用户 ${userId} 的访问令牌不存在`);
    }
    return token || null;
  }

  removeUserToken(userId: string): void {
    this.tokenMap.delete(userId);
    this.logger.log(`移除用户 ${userId} 的访问令牌`);
  }

  hasUserToken(userId: string): boolean {
    return this.tokenMap.has(userId);
  }
}
