import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ested, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';

export class WikiItemDto {
  @IsString()
  @IsNotEmpty()
  node_token: string;

  @IsString()
  @IsNotEmpty()
  title: string;
}

export class CreateTaskDto {
  @IsString()
  @IsNotEmpty()
  space_id: string;

  @IsString()
  @IsNotEmpty()
  target_user_id: string;

  @IsString()
  @IsNotEmpty()
  target_user_name: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WikiItemDto)
  wiki_items: WikiItemDto[];
}
