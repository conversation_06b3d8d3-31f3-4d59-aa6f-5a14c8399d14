import { Controller, Get, Post, Body, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { AuthGuard } from '../auth/auth.guard';
import { TokenStorageService } from '../auth/token-storage.service';

@Controller('tasks')
@UseGuards(AuthGuard)
export class TasksController {
  constructor(
    private readonly tasksService: TasksService,
    private readonly tokenStorage: TokenStorageService,
  ) {}

  @Post()
  async create(@Request() req: any, @Body() createTaskDto: CreateTaskDto) {
    // 存储当前操作用户的access token，用于后续任务处理
    this.tokenStorage.setUserToken(req.user.open_id, req.user.token);

    // 创建任务，传入当前操作用户的ID
    const task = await this.tasksService.createTask(req.user.open_id, createTaskDto);
    return {
      task_id: task.id,
      message: '任务创建成功',
    };
  }

  @Get()
  async findAll(@Request() req: any) {
    return this.tasksService.findAll(req.user.open_id);
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @Request() req: any) {
    return this.tasksService.findOne(+id, req.user.open_id);
  }

  @Delete(':id')
  async cancel(@Param('id') id: string, @Request() req: any) {
    await this.tasksService.cancelTask(+id, req.user.open_id);
    return { message: '任务已取消' };
  }
}
