import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';

import { Task } from '../../entities/task.entity';
import { TaskItem } from '../../entities/task-item.entity';
import { OperationLog } from '../../entities/operation-log.entity';

import { TasksController } from './tasks.controller';
import { TasksService } from './tasks.service';
import { TaskProcessor } from '../../queues/task.processor';
import { TasksGateway } from '../../gateways/tasks.gateway';
import { WikiModule } from '../wiki/wiki.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Task, TaskItem, OperationLog]),
    BullModule.registerQueue({
      name: 'tasks',
    }),
    WikiModule,
    AuthModule,
  ],
  controllers: [TasksController],
  providers: [TasksService, TaskProcessor, TasksGateway],
  exports: [TasksService],
})
export class TasksModule {}
