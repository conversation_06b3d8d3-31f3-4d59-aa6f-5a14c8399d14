import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { InjectQueue } from "@nestjs/bull";
import { Repository } from "typeorm";
import { Queue } from "bull";

import { Task, TaskStatus } from "../../entities/task.entity";
import { TaskItem, TaskItemStatus } from "../../entities/task-item.entity";
import { OperationLog, OperationType } from "../../entities/operation-log.entity";
import { CreateTaskDto } from "./dto/create-task.dto";

@Injectable()
export class TasksService {
  private readonly logger = new Logger(TasksService.name);

  constructor(
    @InjectRepository(Task)
    private taskRepository: Repository<Task>,
    @InjectRepository(TaskItem)
    private taskItemRepository: Repository<TaskItem>,
    @InjectRepository(OperationLog)
    private operationLogRepository: Repository<OperationLog>,
    @InjectQueue("tasks")
    private taskQueue: Queue
  ) {}

  async createTask(currentUserId: string, createTaskDto: CreateTaskDto): Promise<Task> {
    this.logger.log(
      `当前用户 ${currentUserId} 创建任务，将 ${createTaskDto.wiki_items.length} 个文档转移给用户 ${createTaskDto.target_user_id}`
    );

    // 创建任务
    const task = this.taskRepository.create({
      user_id: currentUserId, // 当前操作用户的ID
      space_id: createTaskDto.space_id,
      target_user_id: createTaskDto.target_user_id, // 目标用户ID（文档要转移到的用户）
      target_user_name: createTaskDto.target_user_name,
      total_items: createTaskDto.wiki_items.length,
      status: TaskStatus.PENDING,
    });

    const savedTask = await this.taskRepository.save(task);
    this.logger.log(`任务 ${savedTask.id} 创建成功`);

    // 创建任务项
    const taskItems = createTaskDto.wiki_items.map((item) =>
      this.taskItemRepository.create({
        task_id: savedTask.id,
        node_token: item.node_token,
        wiki_title: item.title,
        status: TaskItemStatus.PENDING,
      })
    );

    await this.taskItemRepository.save(taskItems);
    this.logger.log(`任务 ${savedTask.id} 的 ${taskItems.length} 个任务项创建成功`);

    // 将任务加入队列
    this.taskQueue.add("process-task", { taskId: savedTask.id });
    this.logger.log(`任务 ${savedTask.id} 已加入处理队列`);

    return savedTask;
  }

  async findAll(userId: string): Promise<Task[]> {
    return this.taskRepository.find({
      where: { user_id: userId },
      relations: ["items"],
      order: { created_at: "DESC" },
    });
  }

  async findOne(id: number, userId: string): Promise<Task> {
    return this.taskRepository.findOne({
      where: { id, user_id: userId },
      relations: ["items"],
    });
  }

  async updateTaskStatus(taskId: number, status: TaskStatus): Promise<void> {
    await this.taskRepository.update(taskId, {
      status,
      ...(status === TaskStatus.RUNNING ? { started_at: new Date() } : {}),
      ...(status === TaskStatus.COMPLETED || status === TaskStatus.FAILED
        ? { completed_at: new Date() }
        : {}),
    });
  }

  async updateTaskProgress(taskId: number, completedItems: number, failedItems: number): Promise<void> {
    await this.taskRepository.update(taskId, {
      completed_items: completedItems,
      failed_items: failedItems,
    });
  }

  async updateTaskItemStatus(itemId: number, status: TaskItemStatus, errorMessage?: string): Promise<void> {
    await this.taskItemRepository.update(itemId, {
      status,
      error_message: errorMessage,
      ...(status === TaskItemStatus.COMPLETED || status === TaskItemStatus.FAILED
        ? { processed_at: new Date() }
        : {}),
    });
  }

  async logOperation(
    taskId: number,
    userName: string,
    wikiTitle: string,
    targetUserName: string,
    operationType: OperationType
  ): Promise<void> {
    const log = this.operationLogRepository.create({
      task_id: taskId,
      user_name: userName,
      wiki_title: wikiTitle,
      target_user_name: targetUserName,
      operation_type: operationType,
    });

    await this.operationLogRepository.save(log);
  }

  async cancelTask(taskId: number, userId: string): Promise<void> {
    const task = await this.findOne(taskId, userId);
    if (task && task.status === TaskStatus.PENDING) {
      await this.updateTaskStatus(taskId, TaskStatus.CANCELLED);
      // 从队列中移除任务
      const jobs = await this.taskQueue.getJobs(["waiting", "delayed"]);
      for (const job of jobs) {
        if (job.data.taskId === taskId) {
          await job.remove();
          break;
        }
      }
    }
  }
}
