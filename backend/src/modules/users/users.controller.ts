import { Controller, Get, Query, UseGuards, Request } from '@nestjs/common';
import { WikiService } from '../wiki/wiki.service';
import { AuthGuard } from '../auth/auth.guard';

@Controller('users')
@UseGuards(AuthGuard)
export class UsersController {
  constructor(private readonly wikiService: WikiService) {}

  @Get('search')
  async searchUsers(@Request() req, @Query('query') query: string) {
    return this.wikiService.searchUser(req.user.token, query);
  }

  @Get('me')
  async getCurrentUser(@Request() req) {
    return req.user;
  }
}
