import { Controller, Get, Query, UseGuards, Request } from '@nestjs/common';
import { WikiService } from './wiki.service';
import { AuthGuard } from '../auth/auth.guard';

@Controller('wiki')
@UseGuards(AuthGuard)
export class WikiController {
  constructor(private readonly wikiService: WikiService) {}

  @Get('spaces')
  async getSpaces(@Request() req, @Query('page_token') pageToken?: string) {
    return this.wikiService.getSpaceList(req.user.token, pageToken);
  }

  @Get('list')
  async getWikiList(
    @Request() req,
    @Query('space_id') spaceId: string,
    @Query('page_token') pageToken?: string,
    @Query('parent_node_token') parentNodeToken?: string,
  ) {
    return this.wikiService.getWikiList(req.user.token, spaceId, pageToken, parentNodeToken);
  }
}
