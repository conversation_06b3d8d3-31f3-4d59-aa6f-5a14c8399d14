import { Injectable, Logger } from '@nestjs/common';
import * as Lark from '@larksuiteoapi/node-sdk';
import { writeFileSync } from 'node:fs';

const APP_ID = "cli_a64028e4d4b9d00e";
const APP_SECRET = "8MClAV32Fzoh5VNI9c5iL1y3lUgZMJRG";

@Injectable()
export class WikiService {
  private readonly logger = new Logger(WikiService.name);
  private client: Lark.Client;
  private userInfoMap = new Map<string, any>();

  constructor() {
    this.client = new Lark.Client({
      appId: APP_ID,
      appSecret: APP_SECRET,
      appType: Lark.AppType.SelfBuild,
      domain: Lark.Domain.Feishu,
    });
  }

  async getCurrentUserInfo(token: string) {
    if (this.userInfoMap.has(token)) {
      return this.userInfoMap.get(token);
    }

    try {
      const response = await fetch(`https://open.feishu.cn/open-apis/authen/v1/user_info`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const data = await response.json() as { code: number; msg: string; data: any };
      
      if (data.code !== 0) {
        throw new Error(data.msg);
      }
      
      this.userInfoMap.set(token, data.data);
      return data.data;
    } catch (error) {
      this.logger.error('获取用户信息失败:', error);
      throw error;
    }
  }

  async searchUser(token: string, query: string) {
    try {
      const search = new URLSearchParams();
      search.set("page_size", "100");
      search.set("query", query);
      
      const response = await fetch(`https://open.larkoffice.com/open-apis/search/v1/user?${search.toString()}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      
      const data = await response.json() as { code: number; msg: string; data: { users: any[] } };
      return data.data.users ?? [];
    } catch (error) {
      this.logger.error('搜索用户失败:', error);
      throw error;
    }
  }

  async getSpaceList(token: string, pageToken?: string) {
    try {
      const res = await this.client.wiki.space.list(
        { params: { page_size: 50, page_token: pageToken } },
        Lark.withUserAccessToken(token)
      );
      
      return {
        items: res.data?.items ?? [],
        page_token: res.data?.page_token,
        has_more: res.data?.has_more,
      };
    } catch (error) {
      this.logger.error('获取空间列表失败:', error);
      throw error;
    }
  }

  async getWikiList(token: string, spaceId: string, paginationToken?: string, parentNodeToken?: string) {
    try {
      const res = await this.client.wiki.spaceNode.list(
        {
          path: { space_id: spaceId },
          params: { page_size: 50, page_token: paginationToken, parent_node_token: parentNodeToken },
        },
        Lark.withUserAccessToken(token)
      );
      
      return {
        items: res.data?.items ?? [],
        has_more: res.data?.has_more,
        page_token: res.data?.page_token,
      };
    } catch (error) {
      this.logger.error('获取Wiki列表失败:', error);
      throw error;
    }
  }

  async moveWikiNode(
    userToken: string,
    nodeToken: string,
    memberId: string,
    wikiTitle: string,
    memberName: string
  ) {
    try {
      const currentUserInfo = await this.getCurrentUserInfo(userToken);
      
      if (currentUserInfo?.open_id === memberId) {
        return {
          code: 400,
          msg: "不能转移给自己",
        };
      }

      // 记录操作日志
      writeFileSync(
        "log.txt",
        `${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()} - 用户 ${
          currentUserInfo?.name
        } 转移了文档 ${wikiTitle} 给用户 ${memberName} \n`,
        {
          flag: "a",
        }
      );

      const search = new URLSearchParams();
      search.set("type", "wiki");
      
      const response = await fetch(
        `https://open.feishu.cn/open-apis/drive/v1/permissions/${nodeToken}/members/transfer_owner?${search.toString()}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${userToken}`,
            "Content-Type": "application/json; charset=utf-8",
          },
          body: JSON.stringify({
            member_type: "openid",
            member_id: memberId,
          }),
        }
      );
      
      const data = await response.json() as { code: number; msg: string };
      return data;
    } catch (error) {
      this.logger.error('移动Wiki节点失败:', error);
      return {
        code: 500,
        msg: error.message || '移动失败',
      };
    }
  }
}
