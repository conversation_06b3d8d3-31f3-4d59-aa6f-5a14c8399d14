import { Processor, Process } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Task, TaskStatus } from '../entities/task.entity';
import { TaskItem, TaskItemStatus } from '../entities/task-item.entity';
import { OperationType } from '../entities/operation-log.entity';
import { TasksService } from '../modules/tasks/tasks.service';
import { WikiService } from '../modules/wiki/wiki.service';
import { TasksGateway } from '../gateways/tasks.gateway';
import { TokenStorageService } from '../modules/auth/token-storage.service';

@Processor('tasks')
@Injectable()
export class TaskProcessor {
  private readonly logger = new Logger(TaskProcessor.name);

  constructor(
    @InjectRepository(Task)
    private taskRepository: Repository<Task>,
    @InjectRepository(TaskItem)
    private taskItemRepository: Repository<TaskItem>,
    private tasksService: TasksService,
    private wikiService: WikiService,
    private tasksGateway: TasksGateway,
    private tokenStorage: TokenStorageService,
  ) {}

  @Process('process-task')
  async processTask(job: Job<{ taskId: number }>) {
    const { taskId } = job.data;
    this.logger.log(`开始处理任务 ${taskId}`);

    try {
      // 获取任务和任务项
      const task = await this.taskRepository.findOne({
        where: { id: taskId },
        relations: ['items'],
      });

      if (!task) {
        this.logger.error(`任务 ${taskId} 不存在`);
        return;
      }

      // 获取当前操作用户的访问令牌（用于调用飞书API）
      const currentUserToken = this.tokenStorage.getUserToken(task.user_id);
      if (!currentUserToken) {
        this.logger.error(`当前操作用户 ${task.user_id} 的访问令牌不存在，无法处理任务 ${taskId}`);
        await this.tasksService.updateTaskStatus(taskId, TaskStatus.FAILED);
        return;
      }

      this.logger.log(`开始处理任务 ${taskId}，将文档转移给用户 ${task.target_user_id}`);
      this.logger.log(`使用当前操作用户 ${task.user_id} 的token调用飞书API`);

      // 更新任务状态为运行中
      await this.tasksService.updateTaskStatus(taskId, TaskStatus.RUNNING);
      this.tasksGateway.sendTaskUpdate(task.user_id, {
        task_id: taskId,
        status: TaskStatus.RUNNING,
        completed_items: 0,
        failed_items: 0,
      });

      let completedItems = 0;
      let failedItems = 0;

      // 处理每个任务项
      for (const item of task.items) {
        try {
          // 更新任务项状态为处理中
          await this.tasksService.updateTaskItemStatus(item.id, TaskItemStatus.PROCESSING);

          // 记录开始转移日志
          await this.tasksService.logOperation(
            taskId,
            task.user_id,
            item.wiki_title,
            task.target_user_name,
            OperationType.TRANSFER_START,
          );

          // 调用飞书API转移文档
          const result = await this.wikiService.moveWikiNode(
            currentUserToken, // 使用当前操作用户的access token
            item.node_token,
            task.target_user_id,
            item.wiki_title,
            task.target_user_name,
          );

          if (result.code === 0) {
            // 转移成功
            await this.tasksService.updateTaskItemStatus(item.id, TaskItemStatus.COMPLETED);
            await this.tasksService.logOperation(
              taskId,
              task.user_id,
              item.wiki_title,
              task.target_user_name,
              OperationType.TRANSFER_SUCCESS,
            );
            completedItems++;
          } else {
            // 转移失败
            await this.tasksService.updateTaskItemStatus(item.id, TaskItemStatus.FAILED, result.msg);
            await this.tasksService.logOperation(
              taskId,
              task.user_id,
              item.wiki_title,
              task.target_user_name,
              OperationType.TRANSFER_FAILED,
            );
            failedItems++;
          }

          // 更新任务进度
          await this.tasksService.updateTaskProgress(taskId, completedItems, failedItems);

          // 发送进度更新
          this.tasksGateway.sendTaskUpdate(task.user_id, {
            task_id: taskId,
            status: TaskStatus.RUNNING,
            completed_items: completedItems,
            failed_items: failedItems,
            current_item: {
              node_token: item.node_token,
              title: item.wiki_title,
              status: completedItems > 0 ? TaskItemStatus.COMPLETED : TaskItemStatus.FAILED,
            },
          });

          // 频率限制：每分钟100次，即每600ms一次
          await new Promise(resolve => setTimeout(resolve, 600));

        } catch (error) {
          this.logger.error(`处理任务项 ${item.id} 失败:`, error);
          await this.tasksService.updateTaskItemStatus(item.id, TaskItemStatus.FAILED, error.message);
          failedItems++;
        }
      }

      // 更新最终任务状态
      const finalStatus = failedItems === 0 ? TaskStatus.COMPLETED : 
                         completedItems === 0 ? TaskStatus.FAILED : TaskStatus.COMPLETED;
      
      await this.tasksService.updateTaskStatus(taskId, finalStatus);

      // 发送最终状态更新
      this.tasksGateway.sendTaskUpdate(task.user_id, {
        task_id: taskId,
        status: finalStatus,
        completed_items: completedItems,
        failed_items: failedItems,
      });

      this.logger.log(`任务 ${taskId} 处理完成，成功: ${completedItems}, 失败: ${failedItems}`);

    } catch (error) {
      this.logger.error(`处理任务 ${taskId} 时发生错误:`, error);
      await this.tasksService.updateTaskStatus(taskId, TaskStatus.FAILED);
    }
  }
}
