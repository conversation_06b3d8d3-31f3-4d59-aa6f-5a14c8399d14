import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Subject, Observable, EMPTY, Subscription } from 'rxjs';
import { concatMap, delay, catchError, retry, tap } from 'rxjs/operators';

import { Task, TaskStatus } from '../entities/task.entity';
import { TaskItem, TaskItemStatus } from '../entities/task-item.entity';
import { OperationLog, OperationType } from '../entities/operation-log.entity';
import { WikiService } from '../modules/wiki/wiki.service';
import { TasksGateway } from '../gateways/tasks.gateway';
import { TokenStorageService } from '../modules/auth/token-storage.service';

export interface TaskJob {
  id: string;
  type: string;
  data: any;
  attempts?: number;
  maxAttempts?: number;
}

export interface TaskResult {
  success: boolean;
  error?: string;
  data?: any;
}

@Injectable()
export class TaskManagerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(TaskManagerService.name);
  private taskSubject = new Subject<TaskJob>();
  private taskSubscription: Subscription;
  private activeTasks = new Map<string, TaskJob>();
  private cancelledTasks = new Set<string>();

  constructor(
    @InjectRepository(Task)
    private taskRepository: Repository<Task>,
    @InjectRepository(TaskItem)
    private taskItemRepository: Repository<TaskItem>,
    @InjectRepository(OperationLog)
    private operationLogRepository: Repository<OperationLog>,
    private wikiService: WikiService,
    private tasksGateway: TasksGateway,
    private tokenStorage: TokenStorageService,
  ) {}

  onModuleInit() {
    this.logger.log('初始化任务管理器');
    this.initializeTaskStream();
  }

  onModuleDestroy() {
    this.logger.log('销毁任务管理器');
    if (this.taskSubscription) {
      this.taskSubscription.unsubscribe();
    }
    this.taskSubject.complete();
  }

  private initializeTaskStream() {
    this.taskSubscription = this.taskSubject.pipe(
      concatMap((job) => this.processTaskJob(job).pipe(
        delay(600), // 600ms延迟，符合飞书API每分钟100次的限制
        catchError(error => {
          this.logger.error(`任务 ${job.id} 处理失败:`, error);
          this.activeTasks.delete(job.id);
          return EMPTY;
        })
      ))
    ).subscribe({
      next: (result) => {
        this.logger.log('任务处理完成:', result);
      },
      error: (error) => {
        this.logger.error('任务流发生错误:', error);
      }
    });
  }

  addTask(taskData: any): string {
    const jobId = `task-${taskData.taskId}-${Date.now()}`;
    const job: TaskJob = {
      id: jobId,
      type: 'process-task',
      data: taskData,
      attempts: 0,
      maxAttempts: 3
    };

    this.activeTasks.set(jobId, job);
    this.taskSubject.next(job);
    this.logger.log(`任务 ${jobId} 已加入处理队列`);
    
    return jobId;
  }

  cancelTask(taskId: number): boolean {
    // 查找并取消相关的任务
    for (const [jobId, job] of this.activeTasks.entries()) {
      if (job.data.taskId === taskId) {
        this.cancelledTasks.add(jobId);
        this.activeTasks.delete(jobId);
        this.logger.log(`任务 ${jobId} (taskId: ${taskId}) 已取消`);
        return true;
      }
    }
    return false;
  }

  private processTaskJob(job: TaskJob): Observable<TaskResult> {
    return new Observable<TaskResult>(subscriber => {
      this.processTask(job)
        .then(result => {
          subscriber.next(result);
          subscriber.complete();
        })
        .catch(error => {
          subscriber.error(error);
        });
    }).pipe(
      retry({
        count: job.maxAttempts - 1,
        delay: (error, retryCount) => {
          this.logger.warn(`任务 ${job.id} 第 ${retryCount} 次重试`);
          // 指数退避：2^retryCount * 2000ms
          return new Observable(subscriber => {
            setTimeout(() => {
              subscriber.next(null);
              subscriber.complete();
            }, Math.pow(2, retryCount) * 2000);
          });
        }
      }),
      tap(() => {
        this.activeTasks.delete(job.id);
      })
    );
  }

  private async processTask(job: TaskJob): Promise<TaskResult> {
    const { taskId } = job.data;
    
    // 检查任务是否已被取消
    if (this.cancelledTasks.has(job.id)) {
      this.cancelledTasks.delete(job.id);
      throw new Error(`任务 ${job.id} 已被取消`);
    }

    this.logger.log(`开始处理任务 ${taskId}`);

    try {
      // 获取任务和任务项
      const task = await this.taskRepository.findOne({
        where: { id: taskId },
        relations: ['items'],
      });

      if (!task) {
        throw new Error(`任务 ${taskId} 不存在`);
      }

      // 获取当前操作用户的访问令牌
      const currentUserToken = this.tokenStorage.getUserToken(task.user_id);
      if (!currentUserToken) {
        throw new Error(`当前操作用户 ${task.user_id} 的访问令牌不存在`);
      }

      this.logger.log(`开始处理任务 ${taskId}，将文档转移给用户 ${task.target_user_id}`);

      // 更新任务状态为运行中
      await this.updateTaskStatus(taskId, TaskStatus.RUNNING);
      this.tasksGateway.sendTaskUpdate(task.user_id, {
        task_id: taskId,
        status: TaskStatus.RUNNING,
        completed_items: 0,
        failed_items: 0,
      });

      let completedItems = 0;
      let failedItems = 0;

      // 处理每个任务项
      for (const item of task.items) {
        // 检查任务是否已被取消
        if (this.cancelledTasks.has(job.id)) {
          throw new Error(`任务 ${job.id} 已被取消`);
        }

        try {
          // 更新任务项状态为处理中
          await this.updateTaskItemStatus(item.id, TaskItemStatus.PROCESSING);

          // 记录开始转移日志
          await this.logOperation(
            taskId,
            task.user_id,
            item.wiki_title,
            task.target_user_name,
            OperationType.TRANSFER_START,
          );

          // 调用飞书API转移文档
          const result = await this.wikiService.moveWikiNode(
            currentUserToken,
            item.node_token,
            task.target_user_id,
            item.wiki_title,
            task.target_user_name,
          );

          if (result.code === 0) {
            await this.updateTaskItemStatus(item.id, TaskItemStatus.COMPLETED);
            await this.logOperation(
              taskId,
              task.user_id,
              item.wiki_title,
              task.target_user_name,
              OperationType.TRANSFER_SUCCESS,
            );
            completedItems++;
          } else {
            throw new Error(result.msg || '转移失败');
          }

        } catch (error) {
          this.logger.error(`处理任务项 ${item.id} 失败:`, error);
          await this.updateTaskItemStatus(item.id, TaskItemStatus.FAILED, error.message);
          await this.logOperation(
            taskId,
            task.user_id,
            item.wiki_title,
            task.target_user_name,
            OperationType.TRANSFER_FAILED,
            error.message,
          );
          failedItems++;
        }

        // 更新任务进度
        await this.updateTaskProgress(taskId, completedItems, failedItems);

        // 发送进度更新
        this.tasksGateway.sendTaskUpdate(task.user_id, {
          task_id: taskId,
          status: TaskStatus.RUNNING,
          completed_items: completedItems,
          failed_items: failedItems,
          current_item: {
            node_token: item.node_token,
            title: item.wiki_title,
            status: completedItems > 0 ? TaskItemStatus.COMPLETED : TaskItemStatus.FAILED,
          },
        });
      }

      // 更新最终任务状态
      const finalStatus = failedItems === 0 ? TaskStatus.COMPLETED : 
                         completedItems === 0 ? TaskStatus.FAILED : TaskStatus.COMPLETED;
      
      await this.updateTaskStatus(taskId, finalStatus);

      // 发送最终状态更新
      this.tasksGateway.sendTaskUpdate(task.user_id, {
        task_id: taskId,
        status: finalStatus,
        completed_items: completedItems,
        failed_items: failedItems,
      });

      this.logger.log(`任务 ${taskId} 处理完成，成功: ${completedItems}, 失败: ${failedItems}`);

      return {
        success: true,
        data: { completedItems, failedItems, finalStatus }
      };

    } catch (error) {
      this.logger.error(`处理任务 ${taskId} 时发生错误:`, error);
      await this.updateTaskStatus(taskId, TaskStatus.FAILED);
      throw error;
    }
  }

  // 以下方法需要从 TasksService 中提取或重新实现
  private async updateTaskStatus(taskId: number, status: TaskStatus): Promise<void> {
    await this.taskRepository.update(taskId, { status });
  }

  private async updateTaskItemStatus(itemId: number, status: TaskItemStatus, error?: string): Promise<void> {
    const updateData: any = { status };
    if (error) {
      updateData.error_message = error;
    }
    await this.taskItemRepository.update(itemId, updateData);
  }

  private async updateTaskProgress(taskId: number, completedItems: number, failedItems: number): Promise<void> {
    await this.taskRepository.update(taskId, {
      completed_items: completedItems,
      failed_items: failedItems,
    });
  }

  private async logOperation(
    taskId: number,
    userId: string,
    wikiTitle: string,
    targetUserName: string,
    operationType: OperationType,
    errorMessage?: string,
  ): Promise<void> {
    const operationLog = this.operationLogRepository.create({
      task_id: taskId,
      user_id: userId,
      wiki_title: wikiTitle,
      target_user_name: targetUserName,
      operation_type: operationType,
      error_message: errorMessage,
      created_at: new Date(),
    });

    await this.operationLogRepository.save(operationLog);
    this.logger.log(`操作日志已记录: 任务${taskId}, 用户${userId}, 文档${wikiTitle}, 操作${operationType}`);
  }
}
