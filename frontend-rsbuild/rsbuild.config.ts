import { defineConfig } from '@rsbuild/core';
import { pluginReact } from '@rsbuild/plugin-react';

export default defineConfig({
  plugins: [pluginReact()],
  html: {
    title: '飞书知识库管理系统',
  },
  source: {
    entry: {
      index: './src/index.tsx',
    },
  },
  server: {
    port: 3000,
    cors: true,
  },
  dev: {
    assetPrefix: '/',
  },
  output: {
    assetPrefix: '/',
    cleanDistPath: true,
  },
  resolve: {
    alias: {
      '@': './src',
      '@shared': '../shared',
    },
  },
});
