import { Route, Routes } from "react-router-dom";
import type { Cache } from "swr";
import { SWRConfig } from "swr";
import HomePage from "./pages/HomePage";
import TaskDetailPage from "./pages/TaskDetailPage";
import TasksPage from "./pages/TasksPage";

function localStorageProvider(): Cache {
  // When initializing, we restore the data from `localStorage` into a map.
  const map: Map<string, any> = new Map(JSON.parse(globalThis.localStorage?.getItem("app-cache") || "[]"));

  // Before unloading the app, we write back all the data into `localStorage`.
  window.addEventListener("beforeunload", () => {
    const appCache = JSON.stringify(Array.from(map.entries()));
    localStorage.setItem("app-cache", appCache);
  });

  // We still use the map for write & read for performance.
  return map as Cache;
}

function App() {
  return (
    <SWRConfig value={{ provider: localStorageProvider }}>
      <div className="min-h-screen bg-gray-50">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/tasks" element={<TasksPage />} />
          <Route path="/tasks/:id" element={<TaskDetailPage />} />
        </Routes>
      </div>
    </SWRConfig>
  );
}

export default App;
