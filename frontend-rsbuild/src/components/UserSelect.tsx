import { Select } from "antd";
import { FC, useCallback, useState } from "react";
import { useAsync, useDebounce } from "react-use";
import { useAccessToken } from "../hooks/storage";
import { searchUser } from "../services/sdk";

interface UserSelectProps {
  value?: string;
  onChange: (value: string) => void;
  userName?: string;
  onUserNameChange?: (value: string) => void;
}

export const UserSelect: FC<UserSelectProps> = ({ value, onChange, onUserNameChange }) => {
  const [accessToken] = useAccessToken();
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<{ label: string; value: string }[]>([]);
  const [debounceTimer, setDebounceTimer] = useState<ReturnType<typeof setTimeout> | null>(null);

  const getList = useCallback(async (token: string, searchKey: string) => {
    const reg = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/;
    if (!reg.test(searchKey)) return [];
    if (!searchKey) return [];
    const list = await searchUser(token, searchKey);
    return list.map((item) => ({ label: item.name, value: item.open_id }));
  }, []);

  useAsync(async () => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    const timer = setTimeout(async () => {
      if (accessToken?.token) {
        setLoading(true);
        try {
          const list = await getList(accessToken.token, search);
          console.log(list);
          setOptions(list);
        } finally {
          setLoading(false);
        }
      }
    }, 500);

    setDebounceTimer(timer);
  }, [search, accessToken?.token, getList]);

  const handleChange = useCallback(
    (val: string) => {
      onChange(val);
      if (onUserNameChange) {
        const selectedUser = options.find((item) => item.value === val);
        onUserNameChange(selectedUser?.label || "");
      }
    },
    [onChange, onUserNameChange, options]
  );

  return (
    <Select
      value={value}
      onChange={handleChange}
      showSearch
      filterOption={false}
      placeholder="搜索用户"
      loading={loading}
      onSearch={setSearch}
      options={options}
      popupMatchSelectWidth={false}
    />
  );
};
