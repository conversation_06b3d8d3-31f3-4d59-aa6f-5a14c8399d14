import { useState, useEffect } from 'react';
import lscache from 'lscache';

interface AccessToken {
  token: string;
}

export function useAccessToken(): [AccessToken | null, (token: AccessToken | null) => void] {
  const [accessToken, setAccessTokenState] = useState<AccessToken | null>(null);

  useEffect(() => {
    // 从本地存储加载token
    const storedToken = lscache.get('access_token');
    if (storedToken) {
      setAccessTokenState(storedToken);
    }
  }, []);

  const setAccessToken = (token: AccessToken | null) => {
    setAccessTokenState(token);
    if (token) {
      lscache.set('access_token', token, 60 * 24 * 7); // 7天过期
    } else {
      lscache.remove('access_token');
    }
  };

  return [accessToken, setAccessToken];
}
