import { useCallback, useEffect, useMemo, useState } from "react";
import { But<PERSON>, message, Tree, Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { SpaceSelect } from "../components/SpaceSelect";
import { UserSelect } from "../components/UserSelect";
import { useAccessToken } from "../hooks/storage";
import { getCurrentUserInfo, getAppId, getUserAccessToken, buildAuthUrl, getWikiList } from "../services/sdk";
import { apiService } from "../services/api";
import { UserInfo, WikiItem } from "../services/sdk";

// 获取URL参数
function getUrlParams() {
  const urlParams = new URLSearchParams(window.location.search);
  return {
    code: urlParams.get("code"),
    state: urlParams.get("state"),
  };
}

// 修改树结构的辅助函数
function modifyTree<T extends { children?: T[] }>(
  node: T,
  condition: (node: T) => boolean,
  modification: (node: T) => T
): T {
  // 创建新的节点对象
  const newNode = { ...node };

  // 如果当前节点满足条件，则修改它
  if (condition(node)) {
    Object.assign(newNode, modification(node));
  }

  // 如果有子节点，递归处理
  if (Array.isArray(node.children)) {
    newNode.children = node.children.map((child) => modifyTree(child, condition, modification));
  }

  return newNode;
}

// 扁平化树结构，用于全选功能
function flatTree(node: WikiItem): WikiItem[] {
  const stack = [node];
  const list: WikiItem[] = [];
  while (stack.length) {
    const item = stack.pop();
    if (item?.children?.length) {
      stack.push(...item.children);
    }
    if (item) {
      list.push(item);
    }
  }
  return list;
}

export default function HomePage() {
  const [accessToken, setAccessToken] = useAccessToken();
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [spaceId, setSpaceId] = useState<string>();
  const [selectedUser, setSelectedUser] = useState<string>();
  const [selectedUserName, setSelectedUserName] = useState<string>();
  const [wikiList, setWikiList] = useState<WikiItem[]>();
  const [selectedWikiListOne, setSelectedWikiListOne] = useState<WikiItem[]>([]);
  const [selectedWikiListTwo, setSelectedWikiListTwo] = useState<WikiItem[]>([]);
  const [selectedWikiListThree, setSelectedWikiListThree] = useState<WikiItem[]>([]);
  const [actionLoading, setActionLoading] = useState(false);

  // 计算三列分割的长度
  const treeSliceLength = useMemo(() => Math.round((wikiList?.length ?? 0) / 3), [wikiList]);
  const [loadingNodes, setLoadingNodes] = useState<Set<string>>(new Set());

  const navigate = useNavigate();

  const selectedWikiList = useMemo(
    () => [...selectedWikiListOne, ...selectedWikiListTwo, ...selectedWikiListThree],
    [selectedWikiListOne, selectedWikiListTwo, selectedWikiListThree]
  );

  const handleAuthCallback = useCallback(
    async (code: string) => {
      try {
        const tokenResult = await getUserAccessToken(code);
        if (tokenResult?.token) {
          setAccessToken({ token: tokenResult.token });
          // 清除URL参数
          window.history.replaceState({}, document.title, window.location.pathname);
        } else {
          message.error("获取访问令牌失败");
        }
      } catch (error) {
        message.error("授权失败，请重试");
        console.error("Auth callback error:", error);
      }
    },
    [setAccessToken]
  );

  // 处理飞书授权回调
  useEffect(() => {
    const { code, state } = getUrlParams();
    if (code && state === "feishu-wiki-manage" && !accessToken) {
      handleAuthCallback(code);
    }
  }, [accessToken, handleAuthCallback]);

  const loadUserInfo = useCallback(async () => {
    if (!accessToken?.token) return;
    try {
      const info = await getCurrentUserInfo(accessToken.token);
      setUserInfo(info);
    } catch (error) {
      message.error("获取用户信息失败");
      console.error("Load user info error:", error);
    }
  }, [accessToken?.token]);

  const handleLogin = useCallback(async () => {
    try {
      const appId = await getAppId();
      const redirectUri = window.location.origin + window.location.pathname;
      const authUrl = buildAuthUrl(appId, redirectUri);
      window.location.href = authUrl;
    } catch (error) {
      message.error("获取应用信息失败");
      console.error("Login error:", error);
    }
  }, []);

  // 获取用户信息
  useEffect(() => {
    if (accessToken?.token && !userInfo) {
      loadUserInfo();
    }
  }, [accessToken?.token, userInfo, loadUserInfo]);

  // 获取第一层文档列表（懒加载模式）
  const getInitialList = useCallback(
    async (userId: string, token: string, spaceId: string): Promise<WikiItem[]> => {
      const res = await getWikiList(token, spaceId, undefined, undefined);
      const items = res.items || [];

      return items.map((item: any) => ({
        ...item,
        key: item.node_token,
        disabled: item.owner !== userId,
        isLeaf: !item.has_child, // 设置叶子节点标识
        children: item.has_child ? [] : undefined, // 有子节点的设置空数组，触发懒加载
      }));
    },
    []
  );

  // 懒加载子节点
  const loadChildNodes = useCallback(
    async (nodeToken: string): Promise<WikiItem[]> => {
      if (!accessToken?.token || !userInfo?.open_id || !spaceId) {
        return [];
      }

      try {
        const res = await getWikiList(accessToken.token, spaceId, undefined, nodeToken);
        const items = res.items || [];

        return items.map((item: any) => ({
          ...item,
          key: item.node_token,
          disabled: item.owner !== userInfo.open_id,
          isLeaf: !item.has_child,
          children: item.has_child ? [] : undefined,
        }));
      } catch (error) {
        console.error("Load child nodes error:", error);
        message.error("加载子文档失败");
        return [];
      }
    },
    [accessToken?.token, userInfo?.open_id, spaceId]
  );

  const getList = useCallback(
    async (space_id: string) => {
      if (accessToken?.token && userInfo?.open_id) {
        try {
          const list = await getInitialList(userInfo.open_id, accessToken.token, space_id);
          setWikiList(list);
        } catch (error) {
          message.error("获取文档列表失败");
          console.error("Get wiki list error:", error);
        }
      }
    },
    [accessToken?.token, userInfo?.open_id, getInitialList]
  );

  useEffect(() => {
    if (spaceId) {
      getList(spaceId);
    }
  }, [spaceId, getList]);

  const handleCreateTask = useCallback(async () => {
    if (actionLoading || !accessToken?.token || !spaceId || !selectedUser || !selectedUserName) {
      console.log("创建任务条件不满足:", {
        actionLoading,
        hasToken: !!accessToken?.token,
        spaceId,
        selectedUser,
        selectedUserName,
        selectedCount: selectedWikiList.length,
      });
      return;
    }

    console.log("开始创建任务:", {
      spaceId,
      targetUserId: selectedUser,
      targetUserName: selectedUserName,
      wikiCount: selectedWikiList.length,
    });

    setActionLoading(true);
    try {
      // 创建转移任务
      const taskData = {
        space_id: spaceId,
        target_user_id: selectedUser,
        target_user_name: selectedUserName,
        wiki_items: selectedWikiList.map((item) => ({
          node_token: item.node_token!,
          title: item.title!,
        })),
      };

      console.log("调用API创建任务:", taskData);
      const result = await apiService.createTask(accessToken.token, taskData);
      console.log("任务创建成功:", result);
      message.success(`任务创建成功！任务ID: ${result.task_id}`);

      // 清空选中的文档
      setSelectedWikiListOne([]);
      setSelectedWikiListTwo([]);
      setSelectedWikiListThree([]);

      // 跳转到任务列表页面
      navigate("/tasks");
    } catch (error: any) {
      console.error("创建任务失败:", error);
      message.error(`创建任务失败: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  }, [
    actionLoading,
    accessToken?.token,
    spaceId,
    selectedUser,
    selectedUserName,
    selectedWikiList,
    navigate,
  ]);

  const handleLoadData = useCallback(
    async (node: any) => {
      // 如果节点已经有子节点或者是叶子节点，直接返回
      if (node.children?.length > 0 || node.isLeaf) {
        return Promise.resolve();
      }

      // 如果节点正在加载中，避免重复加载
      if (loadingNodes.has(node.key)) {
        return Promise.resolve();
      }

      try {
        // 设置加载状态
        setLoadingNodes((prev) => new Set(prev).add(node.key));

        // 加载子节点
        const childNodes = await loadChildNodes(node.key);

        // 更新树数据，将子节点添加到对应的父节点
        setWikiList((prevList) => {
          const updateNodeChildren = (nodes: WikiItem[]): WikiItem[] => {
            return nodes.map((item) => {
              if (item.key === node.key) {
                return {
                  ...item,
                  children: childNodes,
                };
              }
              if (item.children) {
                return {
                  ...item,
                  children: updateNodeChildren(item.children),
                };
              }
              return item;
            });
          };

          return updateNodeChildren(prevList || []);
        });

        return Promise.resolve();
      } catch (error) {
        console.error("Load data error:", error);
        message.error(`加载"${node.title}"的子文档失败`);
        return Promise.reject(error);
      } finally {
        // 清除加载状态
        setLoadingNodes((prev) => {
          const newSet = new Set(prev);
          newSet.delete(node.key);
          return newSet;
        });
      }
    },
    [loadChildNodes, loadingNodes]
  );

  // 全选节点下的所有子文档
  const handleSelectAll = useCallback(
    (node: WikiItem, treeIndex: number) => {
      const list = flatTree(node);
      const validItems = list.filter(
        (item) =>
          item.owner === userInfo?.open_id &&
          !selectedWikiList.some((selected) => selected.node_token === item.node_token)
      );

      if (treeIndex === 0) {
        setSelectedWikiListOne((prev) => [...prev, ...validItems]);
      } else if (treeIndex === 1) {
        setSelectedWikiListTwo((prev) => [...prev, ...validItems]);
      } else if (treeIndex === 2) {
        setSelectedWikiListThree((prev) => [...prev, ...validItems]);
      }
    },
    [userInfo?.open_id, selectedWikiList]
  );

  // 创建titleRender函数，用于自定义节点标题渲染
  const createTitleRender = useCallback(
    (treeIndex: number) => {
      return (node: WikiItem) => {
        const isLoading = loadingNodes.has(node.key);
        return (
          <div style={{ display: "flex", alignItems: "center" }}>
            <span style={{ display: "flex", alignItems: "center", gap: "4px" }}>
              {node.title}
              {isLoading && <LoadingOutlined style={{ fontSize: "12px", color: "#1890ff" }} />}
            </span>
            {node.children?.length ? (
              <Button
                className="p-0 ml-3"
                type="link"
                onClick={(e) => {
                  e.stopPropagation();
                  handleSelectAll(node, treeIndex);
                }}
              >
                全选
              </Button>
            ) : null}
          </div>
        );
      };
    },
    [loadingNodes, handleSelectAll]
  );

  // 如果没有访问令牌，显示登录界面
  if (!accessToken) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen gap-4">
        <h1 className="text-2xl font-bold">飞书知识库管理系统</h1>
        <p className="text-gray-600">请先登录飞书账号</p>
        <Button type="primary" onClick={handleLogin}>
          登录飞书
        </Button>
      </div>
    );
  }

  // 如果正在加载用户信息
  if (!userInfo) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div>正在加载用户信息...</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex items-center gap-2">
        <span>选择空间</span>
        <SpaceSelect value={spaceId} onChange={setSpaceId} />
        <span className="mx-2"></span>
        <span>选择新的所有者</span>
        <UserSelect value={selectedUser} onChange={setSelectedUser} onUserNameChange={setSelectedUserName} />
        <span className="mx-3"></span>
        <Button
          type="primary"
          disabled={!selectedUser || !selectedWikiList.length}
          loading={actionLoading}
          onClick={handleCreateTask}
        >
          创建转移任务
        </Button>
        <Button onClick={() => navigate("/tasks")}>查看任务</Button>
        <Button
          danger
          onClick={() => {
            setAccessToken(null);
            window.location.reload();
          }}
        >
          重新授权
        </Button>
      </div>
      {wikiList?.length ? (
        <div className="flex gap-4">
          {/* 第一列 */}
          <Tree
            className="w-1/3"
            treeData={wikiList.slice(0, treeSliceLength)}
            checkable
            checkStrictly={true}
            checkedKeys={selectedWikiList.map((item) => item.key)}
            onCheck={(...[, { checkedNodes }]) => {
              setSelectedWikiListOne(checkedNodes);
            }}
            loadData={handleLoadData}
            titleRender={createTitleRender(0)}
          />

          {/* 第二列 */}
          <Tree
            className="w-1/3"
            treeData={wikiList.slice(treeSliceLength, treeSliceLength * 2)}
            checkable
            checkStrictly={true}
            checkedKeys={selectedWikiList.map((item) => item.key)}
            onCheck={(...[, { checkedNodes }]) => {
              setSelectedWikiListTwo(checkedNodes);
            }}
            loadData={handleLoadData}
            titleRender={createTitleRender(1)}
          />

          {/* 第三列 */}
          <Tree
            className="w-1/3"
            treeData={wikiList.slice(treeSliceLength * 2)}
            checkable
            checkStrictly={true}
            checkedKeys={selectedWikiList.map((item) => item.key)}
            onCheck={(...[, { checkedNodes }]) => {
              setSelectedWikiListThree(checkedNodes);
            }}
            loadData={handleLoadData}
            titleRender={createTitleRender(2)}
          />
        </div>
      ) : (
        spaceId && <div className="text-center text-gray-500">暂无文档</div>
      )}

      {/* 已选择的文档列表 */}
      {selectedWikiList.length > 0 && (
        <div className="mt-6">
          <h3 className="mb-4 text-lg font-medium">已选择的文档 ({selectedWikiList.length})</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 max-h-60 overflow-y-auto">
            {selectedWikiList.map((item) => (
              <div key={item.key} className="p-2 bg-blue-50 border border-blue-200 rounded text-sm">
                {item.title}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
