import React, { useCallback, useEffect, useState } from "react";
import { <PERSON>ton, Card, List, Tag, message, Spin } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import { apiService } from "../services/api";
import { socketService } from "../services/socket";
import { useAccessToken } from "../hooks/storage";
import { Task, TaskItem, TaskStatus, TaskItemStatus, TaskProgressUpdate } from "@shared/types";

const TaskStatusMap = {
  [TaskStatus.PENDING]: { color: "blue", text: "等待中" },
  [TaskStatus.RUNNING]: { color: "orange", text: "执行中" },
  [TaskStatus.COMPLETED]: { color: "green", text: "已完成" },
  [TaskStatus.FAILED]: { color: "red", text: "失败" },
  [TaskStatus.CANCELLED]: { color: "gray", text: "已取消" },
};

const TaskItemStatusMap = {
  [TaskItemStatus.PENDING]: { color: "blue", text: "等待中" },
  [TaskItemStatus.PROCESSING]: { color: "orange", text: "处理中" },
  [TaskItemStatus.COMPLETED]: { color: "green", text: "已完成" },
  [TaskItemStatus.FAILED]: { color: "red", text: "失败" },
};

export default function TaskDetailPage() {
  const [accessToken] = useAccessToken();
  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentItem, setCurrentItem] = useState<string | null>(null);
  const navigate = useNavigate();
  const params = useParams();
  const taskId = parseInt(params.id as string);

  const loadTask = useCallback(async () => {
    if (!accessToken?.token) return;
    try {
      setLoading(true);
      const taskData = await apiService.getTask(accessToken.token, taskId);
      setTask(taskData);
    } catch (error: any) {
      message.error(`加载任务失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  }, [accessToken?.token, taskId]);

  const setupWebSocket = useCallback(() => {
    if (!accessToken?.token) return;

    socketService.connect(accessToken.token);

    socketService.onTaskUpdate((update: TaskProgressUpdate) => {
      if (update.task_id === taskId) {
        setTask((prev) =>
          prev
            ? {
                ...prev,
                status: update.status as TaskStatus,
                completed_items: update.completed_items,
                failed_items: update.failed_items,
              }
            : null
        );

        if (update.current_item) {
          setCurrentItem(update.current_item.node_token);
        }
      }
    });

    socketService.onTaskComplete(({ taskId: completedTaskId }) => {
      if (completedTaskId === taskId) {
        message.success("任务已完成");
        loadTask();
      }
    });

    socketService.onTaskError(({ taskId: errorTaskId, error }) => {
      if (errorTaskId === taskId) {
        message.error(`任务执行失败: ${error}`);
        loadTask();
      }
    });
  }, [accessToken?.token, taskId, loadTask]);

  useEffect(() => {
    if (!accessToken) {
      return;
    }

    loadTask();
    setupWebSocket();

    return () => {
      socketService.disconnect();
    };
  }, [accessToken, taskId, navigate, loadTask, setupWebSocket]);

  if (loading) {
    return (
      <div className="container mx-auto p-6 flex justify-center">
        <Spin size="large" />
      </div>
    );
  }

  if (!task) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h2>任务不存在</h2>
          <Button onClick={() => navigate("/tasks")}>返回任务列表</Button>
        </div>
      </div>
    );
  }

  const statusInfo = TaskStatusMap[task.status];

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">任务详情</h1>
        <Button onClick={() => navigate("/tasks")}>返回任务列表</Button>
      </div>

      <Card className="mb-6">
        <div className="flex items-center gap-3 mb-4">
          <h2 className="text-xl font-medium">转移到 {task.target_user_name}</h2>
          <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
        </div>

        <div className="grid grid-cols-2 gap-4 text-gray-600">
          <div>
            <p>
              <strong>任务ID:</strong> {task.id}
            </p>
            <p>
              <strong>创建时间:</strong> {new Date(task.created_at).toLocaleString()}
            </p>
            {task.started_at && (
              <p>
                <strong>开始时间:</strong> {new Date(task.started_at).toLocaleString()}
              </p>
            )}
            {task.completed_at && (
              <p>
                <strong>完成时间:</strong> {new Date(task.completed_at).toLocaleString()}
              </p>
            )}
          </div>
          <div>
            <p>
              <strong>总文档数:</strong> {task.total_items}
            </p>
            <p>
              <strong>已完成:</strong> {task.completed_items}
            </p>
            <p>
              <strong>失败:</strong> {task.failed_items}
            </p>
            <p>
              <strong>进度:</strong>{" "}
              {Math.round(((task.completed_items + task.failed_items) / task.total_items) * 100)}%
            </p>
          </div>
        </div>

        {task.error_message && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
            <p className="text-red-600">
              <strong>错误信息:</strong> {task.error_message}
            </p>
          </div>
        )}
      </Card>

      <Card title="文档列表">
        <List
          dataSource={task.items || []}
          renderItem={(item: TaskItem) => {
            const itemStatusInfo = TaskItemStatusMap[item.status];
            const isCurrentItem = currentItem === item.node_token;

            return (
              <List.Item className={isCurrentItem ? "bg-blue-50" : ""}>
                <div className="flex justify-between items-center w-full">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{item.wiki_title}</span>
                      {isCurrentItem && <Tag color="blue">正在处理</Tag>}
                    </div>
                    <div className="text-sm text-gray-500">节点Token: {item.node_token}</div>
                    {item.error_message && (
                      <div className="text-sm text-red-500 mt-1">错误: {item.error_message}</div>
                    )}
                    {item.processed_at && (
                      <div className="text-sm text-gray-500 mt-1">
                        处理时间: {new Date(item.processed_at).toLocaleString()}
                      </div>
                    )}
                  </div>
                  <Tag color={itemStatusInfo.color}>{itemStatusInfo.text}</Tag>
                </div>
              </List.Item>
            );
          }}
        />
      </Card>
    </div>
  );
}
