import { Task, TaskProgressUpdate, TaskStatus } from "@shared/types";
import { Button, Card, List, message, Modal, Progress, Tag } from "antd";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAccessToken } from "../hooks/storage";
import { apiService } from "../services/api";
import { socketService } from "../services/socket";

const TaskStatusMap = {
  [TaskStatus.PENDING]: { color: "blue", text: "等待中" },
  [TaskStatus.RUNNING]: { color: "orange", text: "执行中" },
  [TaskStatus.COMPLETED]: { color: "green", text: "已完成" },
  [TaskStatus.FAILED]: { color: "red", text: "失败" },
  [TaskStatus.CANCELLED]: { color: "gray", text: "已取消" },
};

export default function TasksPage() {
  const [accessToken] = useAccessToken();
  console.log(accessToken);

  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [taskProgress, setTaskProgress] = useState<Map<number, TaskProgressUpdate>>(new Map());
  const navigate = useNavigate();

  const loadTasks = useCallback(async () => {
    if (!accessToken?.token) return;
    try {
      setLoading(true);
      const taskList = await apiService.getTasks(accessToken.token);
      setTasks(taskList);
    } catch (error: any) {
      message.error(`加载任务失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  }, [accessToken?.token]);

  const setupWebSocket = useCallback(() => {
    if (!accessToken?.token) return;

    socketService.connect(accessToken.token);

    socketService.onTaskUpdate((update: TaskProgressUpdate) => {
      setTaskProgress((prev) => new Map(prev.set(update.task_id, update)));

      // 更新任务状态
      setTasks((prev) =>
        prev.map((task) =>
          task.id === update.task_id
            ? {
                ...task,
                status: update.status as TaskStatus,
                completed_items: update.completed_items,
                failed_items: update.failed_items,
              }
            : task
        )
      );
    });

    socketService.onTaskComplete(({ taskId }) => {
      message.success(`任务 ${taskId} 已完成`);
      loadTasks(); // 重新加载任务列表
    });

    socketService.onTaskError(({ taskId, error }) => {
      message.error(`任务 ${taskId} 执行失败: ${error}`);
      loadTasks(); // 重新加载任务列表
    });
  }, [accessToken?.token, loadTasks]);

  useEffect(() => {
    if (!accessToken) {
      return;
    }

    loadTasks();
    setupWebSocket();

    return () => {
      socketService.disconnect();
    };
  }, [accessToken, navigate, loadTasks, setupWebSocket]);

  const cancelTask = useCallback(
    async (taskId: number) => {
      if (!accessToken?.token) return;
      try {
        await apiService.cancelTask(accessToken.token, taskId);
        message.success("任务已取消");
        loadTasks();
      } catch (error: any) {
        message.error(`取消任务失败: ${error.message}`);
      }
    },
    [accessToken?.token, loadTasks]
  );

  const showCancelConfirm = useCallback(
    (task: Task) => {
      Modal.confirm({
        title: "确认取消任务",
        content: `确定要取消任务"转移到 ${task.target_user_name}"吗？`,
        onOk: () => cancelTask(task.id),
      });
    },
    [cancelTask]
  );

  const getProgress = useCallback(
    (task: Task) => {
      const progress = taskProgress.get(task.id);
      if (progress) {
        return {
          percent: Math.round((progress.completed_items / task.total_items) * 100),
          completed: progress.completed_items,
          failed: progress.failed_items,
          total: task.total_items,
        };
      }
      return {
        percent: Math.round(((task.completed_items + task.failed_items) / task.total_items) * 100),
        completed: task.completed_items,
        failed: task.failed_items,
        total: task.total_items,
      };
    },
    [taskProgress]
  );

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">任务列表</h1>
        <Button onClick={() => navigate("/")}>返回主页</Button>
      </div>

      <List
        loading={loading}
        dataSource={tasks}
        renderItem={(task) => {
          const statusInfo = TaskStatusMap[task.status];
          const progress = getProgress(task);

          return (
            <List.Item>
              <Card className="w-full">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-medium">转移到 {task.target_user_name}</h3>
                      <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
                    </div>

                    <div className="text-gray-600 mb-3">
                      <p>任务ID: {task.id}</p>
                      <p>创建时间: {new Date(task.created_at).toLocaleString()}</p>
                      <p>文档数量: {task.total_items} 个</p>
                    </div>

                    {task.status === TaskStatus.RUNNING && (
                      <div className="mb-3">
                        <Progress
                          percent={progress.percent}
                          status={progress.failed > 0 ? "exception" : "active"}
                        />
                        <div className="text-sm text-gray-500 mt-1">
                          已完成: {progress.completed} | 失败: {progress.failed} | 总计: {progress.total}
                        </div>
                      </div>
                    )}

                    {task.status === TaskStatus.COMPLETED && (
                      <div className="mb-3">
                        <Progress percent={100} status={task.failed_items > 0 ? "exception" : "success"} />
                        <div className="text-sm text-gray-500 mt-1">
                          成功: {task.completed_items} | 失败: {task.failed_items} | 总计: {task.total_items}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2">
                    {task.status === TaskStatus.PENDING && (
                      <Button danger size="small" onClick={() => showCancelConfirm(task)}>
                        取消
                      </Button>
                    )}
                    <Button size="small" onClick={() => navigate(`/tasks/${task.id}`)}>
                      查看详情
                    </Button>
                  </div>
                </div>
              </Card>
            </List.Item>
          );
        }}
      />
    </div>
  );
}
