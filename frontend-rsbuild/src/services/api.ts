// API服务层 - 与后端通信
import { CreateTaskRequest, CreateTaskResponse, Task, UserInfo } from '@shared/types';

const API_BASE_URL = 'http://localhost:3001/api';

class ApiService {
  private getAuthHeaders(token: string) {
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  // 认证相关
  async getAppId(): Promise<{ app_id: string }> {
    const response = await fetch(`${API_BASE_URL}/auth/app-id`);
    return response.json();
  }

  async getUserAccessToken(code: string): Promise<{ token: string } | null> {
    const response = await fetch(`${API_BASE_URL}/auth/token?code=${code}`);
    return response.json();
  }

  // 用户相关
  async getCurrentUser(token: string): Promise<UserInfo> {
    const response = await fetch(`${API_BASE_URL}/users/me`, {
      headers: this.getAuthHeaders(token),
    });
    return response.json();
  }

  async searchUsers(token: string, query: string): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/users/search?query=${encodeURIComponent(query)}`, {
      headers: this.getAuthHeaders(token),
    });
    return response.json();
  }

  // Wiki相关
  async getSpaceList(token: string, pageToken?: string) {
    const params = new URLSearchParams();
    if (pageToken) params.set('page_token', pageToken);
    
    const response = await fetch(`${API_BASE_URL}/wiki/spaces?${params.toString()}`, {
      headers: this.getAuthHeaders(token),
    });
    return response.json();
  }

  async getWikiList(token: string, spaceId: string, pageToken?: string, parentNodeToken?: string) {
    const params = new URLSearchParams();
    params.set('space_id', spaceId);
    if (pageToken) params.set('page_token', pageToken);
    if (parentNodeToken) params.set('parent_node_token', parentNodeToken);
    
    const response = await fetch(`${API_BASE_URL}/wiki/list?${params.toString()}`, {
      headers: this.getAuthHeaders(token),
    });
    return response.json();
  }

  // 任务相关
  async createTask(token: string, taskData: CreateTaskRequest): Promise<CreateTaskResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/tasks`, {
        method: 'POST',
        headers: this.getAuthHeaders(token),
        body: JSON.stringify(taskData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('创建任务失败:', response.status, errorText);
        throw new Error(`创建任务失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('任务创建成功:', result);
      return result;
    } catch (error) {
      console.error('创建任务请求失败:', error);
      throw error;
    }
  }

  async getTasks(token: string): Promise<Task[]> {
    const response = await fetch(`${API_BASE_URL}/tasks`, {
      headers: this.getAuthHeaders(token),
    });
    return response.json();
  }

  async getTask(token: string, taskId: number): Promise<Task> {
    const response = await fetch(`${API_BASE_URL}/tasks/${taskId}`, {
      headers: this.getAuthHeaders(token),
    });
    return response.json();
  }

  async cancelTask(token: string, taskId: number): Promise<{ message: string }> {
    const response = await fetch(`${API_BASE_URL}/tasks/${taskId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(token),
    });
    return response.json();
  }
}

export const apiService = new ApiService();
