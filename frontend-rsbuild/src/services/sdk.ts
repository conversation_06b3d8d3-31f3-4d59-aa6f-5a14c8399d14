// 前端SDK服务 - 封装飞书相关功能
import { apiService } from "./api";

export interface UserInfo {
  name: string;
  en_name: string;
  avatar_url: string;
  avatar_thumb: string;
  avatar_middle: string;
  avatar_big: string;
  open_id: string;
  union_id: string;
  email: string;
  enterprize_email: string;
  user_id: string;
  mobile: string;
  tenant_key: string;
  employee_no: string;
}

export interface WikiItem {
  node_token: string;
  title: string;
  owner: string;
  has_child: boolean;
  key: string;
  children?: WikiItem[];
  disabled?: boolean;
}

export interface Space {
  space_id: string;
  name: string;
  description?: string;
}

// 获取应用ID
export async function getAppId(): Promise<string> {
  const result = await apiService.getAppId();
  return result.app_id;
}

// 获取用户访问令牌
export async function getUserAccessToken(code: string) {
  return apiService.getUserAccessToken(code);
}

// 获取当前用户信息
export async function getCurrentUserInfo(token: string): Promise<UserInfo> {
  return apiService.getCurrentUser(token);
}

// 搜索用户
export async function searchUser(token: string, query: string) {
  return apiService.searchUsers(token, query);
}

// 获取空间列表
export async function getSpaceList(token: string, pageToken?: string) {
  return apiService.getSpaceList(token, pageToken);
}

// 获取Wiki列表
export async function getWikiList(
  token: string,
  spaceId: string,
  pageToken?: string,
  parentNodeToken?: string
) {
  return apiService.getWikiList(token, spaceId, pageToken, parentNodeToken);
}

// 构建飞书授权URL
export function buildAuthUrl(appId: string, redirectUri: string): string {
  const params = new URLSearchParams({
    app_id: appId,
    redirect_uri: redirectUri,
    scope: "wiki:wiki",
    state: "feishu-wiki-manage",
  });

  return `https://open.feishu.cn/open-apis/authen/v1/authorize?${params.toString()}`;
}
