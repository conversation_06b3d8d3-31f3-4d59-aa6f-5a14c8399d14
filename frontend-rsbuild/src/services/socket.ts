// WebSocket服务 - 实时任务状态更新
import { io, Socket } from 'socket.io-client';
import { TaskProgressUpdate } from '@shared/types';

class SocketService {
  private socket: Socket | null = null;
  private userId: string | null = null;

  connect(userId: string) {
    if (this.socket?.connected && this.userId === userId) {
      return this.socket;
    }

    this.disconnect();
    this.userId = userId;

    this.socket = io('http://localhost:3001', {
      transports: ['websocket'],
    });

    this.socket.on('connect', () => {
      console.log('WebSocket连接成功');
      this.socket?.emit('join-user-room', userId);
    });

    this.socket.on('disconnect', () => {
      console.log('WebSocket连接断开');
    });

    this.socket.on('joined-room', (data) => {
      console.log('加入用户房间成功:', data);
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.userId = null;
    }
  }

  onTaskUpdate(callback: (update: TaskProgressUpdate) => void) {
    this.socket?.on('task-update', callback);
  }

  onTaskComplete(callback: (data: { taskId: number }) => void) {
    this.socket?.on('task-complete', callback);
  }

  onTaskError(callback: (data: { taskId: number; error: string }) => void) {
    this.socket?.on('task-error', callback);
  }

  offTaskUpdate(callback?: (update: TaskProgressUpdate) => void) {
    this.socket?.off('task-update', callback);
  }

  offTaskComplete(callback?: (data: { taskId: number }) => void) {
    this.socket?.off('task-complete', callback);
  }

  offTaskError(callback?: (data: { taskId: number; error: string }) => void) {
    this.socket?.off('task-error', callback);
  }

  isConnected(): boolean {
    return this.socket?.connected ?? false;
  }
}

export const socketService = new SocketService();
