// 共享类型定义

export interface UserInfo {
  name: string;
  en_name: string;
  avatar_url: string;
  avatar_thumb: string;
  avatar_middle: string;
  avatar_big: string;
  open_id: string;
  union_id: string;
  email: string;
  enterprize_email: string;
  user_id: string;
  mobile: string;
  tenant_key: string;
  employee_no: string;
}

export interface WikiItem {
  node_token: string;
  title: string;
  owner: string;
  has_child: boolean;
  key: string;
  children?: WikiItem[];
  disabled?: boolean;
}

export interface Space {
  space_id: string;
  name: string;
  description?: string;
}

// 任务相关类型
export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum TaskItemStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export interface Task {
  id: number;
  user_id: string;
  space_id: string;
  target_user_id: string;
  target_user_name: string;
  status: TaskStatus;
  total_items: number;
  completed_items: number;
  failed_items: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  items?: TaskItem[];
}

export interface TaskItem {
  id: number;
  task_id: number;
  node_token: string;
  wiki_title: string;
  status: TaskItemStatus;
  error_message?: string;
  processed_at?: string;
}

export interface OperationLog {
  id: number;
  task_id?: number;
  user_name: string;
  wiki_title: string;
  target_user_name: string;
  operation_type: 'transfer_start' | 'transfer_success' | 'transfer_failed';
  created_at: string;
}

// API 请求/响应类型
export interface CreateTaskRequest {
  space_id: string;
  target_user_id: string;
  target_user_name: string;
  wiki_items: {
    node_token: string;
    title: string;
  }[];
}

export interface CreateTaskResponse {
  task_id: number;
  message: string;
}

export interface TaskProgressUpdate {
  task_id: number;
  status: TaskStatus;
  completed_items: number;
  failed_items: number;
  current_item?: {
    node_token: string;
    title: string;
    status: TaskItemStatus;
  };
}

// 飞书API响应类型
export interface FeishuApiResponse<T = any> {
  code: number;
  msg: string;
  data?: T;
}

export interface AccessToken {
  token: string;
}
