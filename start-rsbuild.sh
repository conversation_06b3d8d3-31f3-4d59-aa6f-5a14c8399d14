#!/bin/bash

# 飞书知识库管理系统启动脚本 - Rsbuild版本

echo "🚀 启动飞书知识库管理系统 (Rsbuild版本)..."

# 检查Node.js版本
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"

# 启动后端服务
echo "📡 启动后端服务..."
cd backend
if [ ! -d "node_modules" ]; then
    echo "📦 安装后端依赖..."
    npm install
fi

# 后台启动后端
npm run start:dev &
BACKEND_PID=$!
echo "✅ 后端服务已启动 (PID: $BACKEND_PID)"

# 等待后端启动
echo "⏳ 等待后端服务启动..."
sleep 5

# 检查后端是否启动成功
if curl -s http://localhost:3001/api/auth/app-id > /dev/null; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动Rsbuild前端服务
echo "🌐 启动Rsbuild前端服务..."
cd ../frontend-rsbuild
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 后台启动前端
npm run dev &
FRONTEND_PID=$!
echo "✅ Rsbuild前端服务已启动 (PID: $FRONTEND_PID)"

# 等待前端启动
echo "⏳ 等待前端服务启动..."
sleep 3

# 检查前端是否启动成功
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Rsbuild前端服务启动成功"
else
    echo "❌ Rsbuild前端服务启动失败"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit 1
fi

echo ""
echo "🎉 系统启动完成！"
echo "📱 前端地址 (Rsbuild): http://localhost:3000"
echo "🔧 后端地址: http://localhost:3001"
echo ""
echo "🔄 技术栈升级完成："
echo "   ✅ 前端: Next.js → Rsbuild + React"
echo "   ✅ 后端: NestJS + SQLite + Bull Queue"
echo "   ✅ 实时通信: WebSocket"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 创建停止函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    echo "✅ 所有服务已停止"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 保持脚本运行
wait
