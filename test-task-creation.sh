#!/bin/bash

# 飞书知识库管理系统 - 任务创建测试脚本

echo "🧪 开始测试任务创建和执行功能..."

# 检查后端服务是否运行
echo "📡 检查后端服务状态..."
if curl -s http://localhost:3001/api/auth/app-id > /dev/null; then
    echo "✅ 后端服务正常运行"
else
    echo "❌ 后端服务未运行，请先启动后端服务"
    exit 1
fi

# 检查前端服务是否运行
echo "🌐 检查前端服务状态..."
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ 前端服务正常运行"
else
    echo "❌ 前端服务未运行，请先启动前端服务"
    exit 1
fi

echo ""
echo "🔧 修复内容总结："
echo "1. ✅ 添加了TokenStorageService来存储用户access token"
echo "2. ✅ 修正了AuthGuard，确保token正确传递"
echo "3. ✅ 更新了TasksController，在创建任务时存储当前用户token"
echo "4. ✅ 修正了TaskProcessor，使用当前操作用户的token调用飞书API"
echo "5. ✅ 澄清了数据流：user_id=当前操作用户，target_user_id=目标用户"
echo "6. ✅ 改进了前端和后端的错误处理和日志记录"
echo "7. ✅ 优化了Bull队列配置，增加重试机制"

echo ""
echo "📋 测试步骤："
echo "1. 打开浏览器访问 http://localhost:3000"
echo "2. 完成飞书登录授权"
echo "3. 选择空间和目标用户"
echo "4. 选择要转移的文档"
echo "5. 点击'创建转移任务'按钮"
echo "6. 观察控制台日志和任务执行情况"

echo ""
echo "🔍 调试信息位置："
echo "- 前端控制台：浏览器开发者工具 Console"
echo "- 后端日志：终端中的NestJS服务日志"
echo "- 任务队列：查看Bull队列处理日志"

echo ""
echo "🚀 如果问题仍然存在，请检查："
echo "1. 飞书应用配置是否正确"
echo "2. 用户是否有足够的权限转移文档"
echo "3. 网络连接是否正常"
echo "4. 数据库连接是否正常"

echo ""
echo "✨ 测试准备完成！请按照上述步骤进行测试。"
